using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Xunit;
using EnvizonController.Domain.Aggregates;
using EnvizonController.Domain.Repositories;
using EnvizonController.Application.DeviceCommands.Configuration;
using EnvizonController.Application.DeviceCommands.SmartMatching;
using EnvizonController.Application.DeviceCommands.Services;
using EnvizonController.Shared.Enums;

namespace EnvizonController.Application.DeviceCommands.Tests
{
    /// <summary>
    /// ProtocolCommandResolver测试类
    /// 用于验证ValueMappings匹配功能的改进
    /// </summary>
    public class ProtocolCommandResolverTests
    {
        private readonly Mock<IDeviceRepository> _deviceRepositoryMock;
        private readonly Mock<IProtocolRepository> _protocolRepositoryMock;
        private readonly Mock<ILogger<ProtocolCommandResolver>> _loggerMock;
        private readonly Mock<ILogger<SmartMatchingEngine>> _smartMatchingLoggerMock;
        private readonly ProtocolCommandResolver _resolver;

        public ProtocolCommandResolverTests()
        {
            _deviceRepositoryMock = new Mock<IDeviceRepository>();
            _protocolRepositoryMock = new Mock<IProtocolRepository>();
            _loggerMock = new Mock<ILogger<ProtocolCommandResolver>>();
            _smartMatchingLoggerMock = new Mock<ILogger<SmartMatchingEngine>>();

            var config = new CommandMappingConfig
            {
                PredefinedCommands = new Dictionary<string, CommandMapping>(),
                SmartMatchingRules = new List<SmartMatchingRule>()
            };

            var smartMatchingEngine = new SmartMatchingEngine(_smartMatchingLoggerMock.Object, config);
            
            _resolver = new ProtocolCommandResolver(
                _deviceRepositoryMock.Object,
                _protocolRepositoryMock.Object,
                smartMatchingEngine,
                Options.Create(config),
                _loggerMock.Object);
        }

        [Fact]
        public async Task ResolveCommandToProtocolItemAsync_ShouldFindProtocolItemByValueMapping()
        {
            // Arrange
            var deviceId = 1L;
            var commandName = "暂停";

            var protocolItem = new ProtocolItem
            {
                Index = 1,
                Name = "运行状态",
                DisplayName = "运行状态",
                UnitId = 1,
                Address = 3057,
                DataType = DataType.Enum,
                IsWritable = true,
                GroupName = "运行设置",
                ValueMappings = new Dictionary<int, string>
                {
                    { 0, "停止" },
                    { 1, "运行" },
                    { 2, "暂停" }
                }
            };

            var protocol = new Protocol
            {
                Id = 1,
                Name = "测试协议",
                Items = new List<ProtocolItem> { protocolItem }
            };

            var device = new Device
            {
                Id = deviceId,
                Name = "测试设备",
                ProtocolId = 1,
                Protocol = protocol
            };

            _deviceRepositoryMock.Setup(x => x.GetByIdAsync(deviceId))
                .ReturnsAsync(device);

            // Act
            var result = await _resolver.ResolveCommandToProtocolItemAsync(deviceId, commandName);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("运行状态", result.Name);
            Assert.Equal("运行状态", result.DisplayName);
        }

        [Fact]
        public async Task FindProtocolItemByValueMappingAsync_ShouldReturnCorrectProtocolItem()
        {
            // Arrange
            var deviceId = 1L;
            var commandValue = "暂停";

            var protocolItem = new ProtocolItem
            {
                Index = 1,
                Name = "运行状态",
                DisplayName = "运行状态",
                ValueMappings = new Dictionary<int, string>
                {
                    { 0, "停止" },
                    { 1, "运行" },
                    { 2, "暂停" }
                }
            };

            var protocol = new Protocol
            {
                Id = 1,
                Name = "测试协议",
                Items = new List<ProtocolItem> { protocolItem }
            };

            var device = new Device
            {
                Id = deviceId,
                Name = "测试设备",
                ProtocolId = 1,
                Protocol = protocol
            };

            _deviceRepositoryMock.Setup(x => x.GetByIdAsync(deviceId))
                .ReturnsAsync(device);

            // Act
            var result = await _resolver.FindProtocolItemByValueMappingAsync(deviceId, commandValue);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("运行状态", result.Name);
        }

        [Fact]
        public async Task GetCommandValueMappingAsync_ShouldReturnMappingInfo()
        {
            // Arrange
            var deviceId = 1L;
            var commandName = "暂停";

            var protocolItem = new ProtocolItem
            {
                Index = 1,
                Name = "运行状态",
                DisplayName = "运行状态",
                ValueMappings = new Dictionary<int, string>
                {
                    { 0, "停止" },
                    { 1, "运行" },
                    { 2, "暂停" }
                }
            };

            var protocol = new Protocol
            {
                Id = 1,
                Name = "测试协议",
                Items = new List<ProtocolItem> { protocolItem }
            };

            var device = new Device
            {
                Id = deviceId,
                Name = "测试设备",
                ProtocolId = 1,
                Protocol = protocol
            };

            _deviceRepositoryMock.Setup(x => x.GetByIdAsync(deviceId))
                .ReturnsAsync(device);

            // Act
            var result = await _resolver.GetCommandValueMappingAsync(deviceId, commandName);

            // Assert
            Assert.NotNull(result.protocolItem);
            Assert.Equal("运行状态", result.protocolItem.Name);
            Assert.Equal(2, result.mappingKey);
            Assert.Equal("暂停", result.mappingValue);
        }

        [Fact]
        public async Task ResolveCommandToProtocolItemAsync_ShouldReturnNull_WhenNoMatch()
        {
            // Arrange
            var deviceId = 1L;
            var commandName = "不存在的命令";

            var protocolItem = new ProtocolItem
            {
                Index = 1,
                Name = "运行状态",
                DisplayName = "运行状态",
                ValueMappings = new Dictionary<int, string>
                {
                    { 0, "停止" },
                    { 1, "运行" },
                    { 2, "暂停" }
                }
            };

            var protocol = new Protocol
            {
                Id = 1,
                Name = "测试协议",
                Items = new List<ProtocolItem> { protocolItem }
            };

            var device = new Device
            {
                Id = deviceId,
                Name = "测试设备",
                ProtocolId = 1,
                Protocol = protocol
            };

            _deviceRepositoryMock.Setup(x => x.GetByIdAsync(deviceId))
                .ReturnsAsync(device);

            // Act
            var result = await _resolver.ResolveCommandToProtocolItemAsync(deviceId, commandName);

            // Assert
            Assert.Null(result);
        }
    }
}
